import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function parseSDASINSNotes() {
  try {
    console.log('Starting t_sdasins notes parsing...\n');

    // First, get reference data for matching
    console.log('Loading reference data...');
    
    const { data: brands, error: brandsError } = await supabase
      .from('t_brands')
      .select('id, brand');
    
    if (brandsError) {
      console.error('Error fetching brands:', brandsError);
      return;
    }

    const { data: molds, error: moldsError } = await supabase
      .from('t_molds')
      .select('id, mold');
    
    if (moldsError) {
      console.error('Error fetching molds:', moldsError);
      return;
    }

    const { data: plastics, error: plasticsError } = await supabase
      .from('t_plastics')
      .select('id, plastic');
    
    if (plasticsError) {
      console.error('Error fetching plastics:', plasticsError);
      return;
    }

    console.log(`Loaded ${brands.length} brands, ${molds.length} molds, ${plastics.length} plastics\n`);

    // Create lookup maps for faster searching
    const brandMap = new Map();
    brands.forEach(brand => {
      brandMap.set(brand.brand.toLowerCase(), brand);
    });

    const moldMap = new Map();
    molds.forEach(mold => {
      moldMap.set(mold.mold.toLowerCase(), mold);
    });

    const plasticMap = new Map();
    plastics.forEach(plastic => {
      plasticMap.set(plastic.plastic.toLowerCase(), plastic);
    });

    // Handle special cases first
    await handleSpecialCases();

    // Get records that need parsing (have notes but no parsed data)
    const { data: recordsToParse, error: recordsError } = await supabase
      .from('t_sdasins')
      .select('id, notes')
      .not('notes', 'is', null)
      .is('parsed_brand', null)
      .limit(100); // Process in batches

    if (recordsError) {
      console.error('Error fetching records to parse:', recordsError);
      return;
    }

    console.log(`Found ${recordsToParse.length} records to parse\n`);

    let processedCount = 0;
    let updatedCount = 0;

    for (const record of recordsToParse) {
      const parsed = parseNotes(record.notes, brandMap, moldMap, plasticMap);
      
      if (parsed.hasUpdates) {
        const updateData = {};
        if (parsed.brand) updateData.parsed_brand = parsed.brand;
        if (parsed.mold) updateData.parsed_mold = parsed.mold;
        if (parsed.plastic) updateData.parsed_plastic = parsed.plastic;
        if (parsed.minWeight) updateData.parsed_min_weight = parsed.minWeight;
        if (parsed.maxWeight) updateData.parsed_max_weight = parsed.maxWeight;
        if (parsed.remainingNotes !== record.notes) updateData.notes = parsed.remainingNotes;

        const { error: updateError } = await supabase
          .from('t_sdasins')
          .update(updateData)
          .eq('id', record.id);

        if (updateError) {
          console.error(`Error updating record ${record.id}:`, updateError);
        } else {
          updatedCount++;
          console.log(`Updated record ${record.id}: ${JSON.stringify(updateData)}`);
        }
      }

      processedCount++;
      if (processedCount % 10 === 0) {
        console.log(`Processed ${processedCount}/${recordsToParse.length} records...`);
      }
    }

    console.log(`\nCompleted! Processed ${processedCount} records, updated ${updatedCount} records.`);

  } catch (error) {
    console.error('Error:', error);
  }
}

async function handleSpecialCases() {
  console.log('Handling special cases...');

  const specialCases = [
    { contains: 'Elevon', replacement: 'XXXX Elevon' },
    { contains: 'Backpack', replacement: 'XXXX Accessory' },
    { contains: 'Doomsday', replacement: 'XXXX Doomsday' },
    { contains: 'Disc Golf Bag', replacement: 'XXXX Accessory' }
  ];

  for (const specialCase of specialCases) {
    const { error } = await supabase
      .from('t_sdasins')
      .update({ notes: specialCase.replacement })
      .like('notes', `%${specialCase.contains}%`);

    if (error) {
      console.error(`Error updating special case ${specialCase.contains}:`, error);
    } else {
      console.log(`Updated records containing "${specialCase.contains}"`);
    }
  }

  // Handle "Infinite" at beginning
  const { error: infiniteError } = await supabase
    .from('t_sdasins')
    .update({ notes: 'XXXX Infinite' })
    .like('notes', 'Infinite%');

  if (infiniteError) {
    console.error('Error updating Infinite records:', infiniteError);
  } else {
    console.log('Updated records beginning with "Infinite"');
  }

  console.log('Special cases handled.\n');
}

function parseNotes(notes, brandMap, moldMap, plasticMap) {
  if (!notes || notes.startsWith('XXXX')) {
    return { hasUpdates: false };
  }

  const result = {
    brand: null,
    mold: null,
    plastic: null,
    minWeight: null,
    maxWeight: null,
    remainingNotes: notes,
    hasUpdates: false
  };

  let workingNotes = notes;

  // Extract weight ranges (e.g., "173-174g", "170-175g")
  const weightMatch = workingNotes.match(/(\d{3})-(\d{3})g?/);
  if (weightMatch) {
    result.minWeight = parseInt(weightMatch[1]);
    result.maxWeight = parseInt(weightMatch[2]);
    workingNotes = workingNotes.replace(weightMatch[0], '').trim();
    result.hasUpdates = true;
  }

  // Try to find brand matches
  for (const [brandName, brandData] of brandMap) {
    if (workingNotes.toLowerCase().includes(brandName)) {
      result.brand = brandData.brand;
      workingNotes = workingNotes.replace(new RegExp(brandName, 'gi'), '').trim();
      result.hasUpdates = true;
      break;
    }
  }

  // Try to find mold matches
  for (const [moldName, moldData] of moldMap) {
    if (workingNotes.toLowerCase().includes(moldName)) {
      result.mold = moldData.mold;
      workingNotes = workingNotes.replace(new RegExp(moldName, 'gi'), '').trim();
      result.hasUpdates = true;
      break;
    }
  }

  // Try to find plastic matches
  for (const [plasticName, plasticData] of plasticMap) {
    if (workingNotes.toLowerCase().includes(plasticName)) {
      result.plastic = plasticData.plastic;
      workingNotes = workingNotes.replace(new RegExp(plasticName, 'gi'), '').trim();
      result.hasUpdates = true;
      break;
    }
  }

  // Clean up remaining notes
  result.remainingNotes = workingNotes
    .replace(/\s+/g, ' ')
    .replace(/^\s*[-|,]\s*/, '')
    .replace(/\s*[-|,]\s*$/, '')
    .trim();

  return result;
}

// Run the parser
parseSDASINSNotes();
