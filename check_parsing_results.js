import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkParsingResults() {
  try {
    console.log('Checking t_sdasins parsing results...\n');

    // Get total counts
    const { count: totalRecords, error: totalError } = await supabase
      .from('t_sdasins')
      .select('*', { count: 'exact', head: true });

    if (totalError) {
      console.error('Error getting total count:', totalError);
      return;
    }

    // Get records with notes
    const { count: withNotes, error: notesError } = await supabase
      .from('t_sdasins')
      .select('*', { count: 'exact', head: true })
      .not('notes', 'is', null);

    if (notesError) {
      console.error('Error getting notes count:', notesError);
      return;
    }

    // Get records with raw_notes (processed)
    const { count: withRawNotes, error: rawNotesError } = await supabase
      .from('t_sdasins')
      .select('*', { count: 'exact', head: true })
      .not('raw_notes', 'is', null);

    if (rawNotesError) {
      console.error('Error getting raw_notes count:', rawNotesError);
      return;
    }

    // Get records with parsed data
    const { count: withParsedBrand, error: brandError } = await supabase
      .from('t_sdasins')
      .select('*', { count: 'exact', head: true })
      .not('parsed_brand', 'is', null);

    const { count: withParsedMold, error: moldError } = await supabase
      .from('t_sdasins')
      .select('*', { count: 'exact', head: true })
      .not('parsed_mold', 'is', null);

    const { count: withParsedPlastic, error: plasticError } = await supabase
      .from('t_sdasins')
      .select('*', { count: 'exact', head: true })
      .not('parsed_plastic', 'is', null);

    const { count: withParsedWeight, error: weightError } = await supabase
      .from('t_sdasins')
      .select('*', { count: 'exact', head: true })
      .not('parsed_min_weight', 'is', null);

    // Get records still needing parsing
    const { count: needsParsing, error: needsError } = await supabase
      .from('t_sdasins')
      .select('*', { count: 'exact', head: true })
      .not('notes', 'is', null)
      .is('parsed_brand', null);

    console.log('=== PARSING RESULTS SUMMARY ===');
    console.log(`Total t_sdasins records: ${totalRecords}`);
    console.log(`Records with notes: ${withNotes}`);
    console.log(`Records with raw_notes (processed): ${withRawNotes}`);
    console.log(`Records still needing parsing: ${needsParsing}`);
    console.log('');
    console.log('=== PARSED DATA COUNTS ===');
    console.log(`Records with parsed brand: ${withParsedBrand}`);
    console.log(`Records with parsed mold: ${withParsedMold}`);
    console.log(`Records with parsed plastic: ${withParsedPlastic}`);
    console.log(`Records with parsed weight: ${withParsedWeight}`);
    console.log('');

    // Get some sample parsed records
    const { data: sampleParsed, error: sampleError } = await supabase
      .from('t_sdasins')
      .select('id, raw_notes, notes, parsed_brand, parsed_mold, parsed_plastic, parsed_min_weight, parsed_max_weight')
      .not('parsed_brand', 'is', null)
      .limit(10);

    if (sampleError) {
      console.error('Error getting sample data:', sampleError);
    } else {
      console.log('=== SAMPLE PARSED RECORDS ===');
      sampleParsed.forEach(record => {
        console.log(`\nID ${record.id}:`);
        console.log(`  Original: ${record.raw_notes}`);
        console.log(`  Remaining: ${record.notes}`);
        console.log(`  Brand: ${record.parsed_brand || 'N/A'}`);
        console.log(`  Mold: ${record.parsed_mold || 'N/A'}`);
        console.log(`  Plastic: ${record.parsed_plastic || 'N/A'}`);
        console.log(`  Weight: ${record.parsed_min_weight || 'N/A'}-${record.parsed_max_weight || 'N/A'}`);
      });
    }

    // Get top brands found
    const { data: topBrands, error: topBrandsError } = await supabase
      .from('t_sdasins')
      .select('parsed_brand')
      .not('parsed_brand', 'is', null);

    if (!topBrandsError && topBrands) {
      const brandCounts = {};
      topBrands.forEach(record => {
        brandCounts[record.parsed_brand] = (brandCounts[record.parsed_brand] || 0) + 1;
      });

      const sortedBrands = Object.entries(brandCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10);

      console.log('\n=== TOP 10 PARSED BRANDS ===');
      sortedBrands.forEach(([brand, count]) => {
        console.log(`${brand}: ${count} records`);
      });
    }

    // Get records with unparsed data (for manual review)
    const { data: unparsedSample, error: unparsedError } = await supabase
      .from('t_sdasins')
      .select('id, notes')
      .not('notes', 'is', null)
      .is('parsed_brand', null)
      .not('notes', 'like', 'XXXX%')
      .limit(20);

    if (!unparsedError && unparsedSample) {
      console.log('\n=== SAMPLE UNPARSED RECORDS (for manual review) ===');
      unparsedSample.forEach(record => {
        console.log(`ID ${record.id}: ${record.notes}`);
      });
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the check
checkParsingResults();
